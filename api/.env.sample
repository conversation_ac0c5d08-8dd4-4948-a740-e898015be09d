NODE_ENV=production
HTTP_PORT=8080
TOKEN_SECRET=ranrom-secret-key
MONGO_URI=mongodb://localhost/xfans

# skip https validate in case cert is invalid
REJECT_UNAUTHORIZED=false

# Redis server configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB=0
# redis prefix, use in some case
REDIS_PREFIX=queue_

# API url which will be used for file and other settings
BASE_URL=http://api.mydomain.com

# Video Converter API URL
VIDEO_CONVERTER_API_URL=http://localhost:8083

# Use for seed and some features
DOMAIN=mydomain.com
USER_URL=https://mydomain.com

## Email settings
MAILER_CONCURRENCY=2

#EMAIL_VERIFIED_SUCCESS_URL=http(s)://[domain]/auth/email-verified-success
EMAIL_VERIFIED_SUCCESS_URL=https://xfans.biz/auth/email-verified-success

# enter num of CPU/processes if use cluster mode. example USE_CLUSTER=4. Leave empty if dont want to use
USE_CLUSTER=